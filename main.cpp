#include <windows.h>
#include "Calculator.h"

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // 初始化计算器
    Calculator calculator;

    if (!calculator.Initialize(hInstance)) {
        MessageBoxA(nullptr, "Cannot initialize calculator window", "Error", MB_OK | MB_ICONERROR);
        return -1;
    }

    // 显示窗口
    calculator.Show(nCmdShow);

    // 消息循环
    MSG msg = {};
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}
