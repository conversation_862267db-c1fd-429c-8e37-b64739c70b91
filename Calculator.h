#ifndef CALCULATOR_H
#define CALCULATOR_H

#include <windows.h>
#include <string>
#include "resource.h"

class Calculator {
private:
    // 窗口和控件句柄
    HWND hWnd;
    HWND hDisplay;
    HWND hButtons[20];  // 存储所有按钮的句柄
    HFONT hFont;        // 字体句柄
    
    // 计算状态
    double currentValue;
    double previousValue;
    char currentOperator;
    bool isNewNumber;
    bool hasDecimal;
    bool hasError;
    
    // 显示字符串
    std::string displayText;
    
    // 私有方法
    void CreateControls();
    void CreateButton(int id, const char* text, int x, int y, int width = BUTTON_WIDTH, int height = BUTTON_HEIGHT);
    void UpdateDisplay();
    void HandleNumber(int digit);
    void HandleOperator(char op);
    void HandleEqual();
    void HandleClear();
    void HandleClearEntry();
    void HandleDecimal();
    void HandleBackspace();
    double PerformCalculation(double a, double b, char op);
    void ShowError(const char* message);
    void ResetCalculator();
    
public:
    Calculator();
    ~Calculator();
    
    // 公共方法
    bool Initialize(HINSTANCE hInstance);
    void Show(int nCmdShow);
    LRESULT HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam);
    HWND GetWindowHandle() const { return hWnd; }
    
    // 静态窗口过程
    static LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
};

#endif // CALCULATOR_H
