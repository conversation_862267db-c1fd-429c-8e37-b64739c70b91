# Windows GUI 计算器

这是一个使用C++和Windows API开发的原生Windows图形用户界面计算器应用程序。

## 功能特性

### 基本功能
- ✅ 基本四则运算（加、减、乘、除）
- ✅ 支持小数点运算
- ✅ 清除功能（C - 清除全部，CE - 清除当前输入）
- ✅ 退格功能（删除最后输入的字符）
- ✅ 实时显示计算结果

### 界面特性
- ✅ 类似Windows系统计算器的布局
- ✅ 合适的按钮大小和间距
- ✅ 清晰的数字显示区域
- ✅ 响应式按钮设计

### 输入支持
- ✅ 鼠标点击按钮
- ✅ 键盘输入支持：
  - 数字键 0-9
  - 运算符 +、-、*、/
  - 回车键或空格键执行等号功能
  - 小数点键 .
  - 退格键删除字符
  - ESC键清除全部
  - Delete键清除当前输入

### 错误处理
- ✅ 除零错误检测
- ✅ 输入长度限制
- ✅ 无效操作处理

## 文件结构

```
├── main.cpp          # 程序入口点
├── Calculator.h      # 计算器类声明
├── Calculator.cpp    # 计算器类实现
├── resource.h        # 资源和常量定义
├── calculator.exe    # 编译后的可执行文件
└── README.md         # 说明文档
```

## 编译要求

### 系统要求
- Windows 操作系统
- MinGW-w64 或 Microsoft Visual C++ 编译器
- Windows SDK（包含Windows API头文件）

### 编译命令

使用MinGW-w64编译：
```bash
g++ -o calculator.exe main.cpp Calculator.cpp -luser32 -lgdi32 -mwindows
```

使用Visual Studio编译：
```bash
cl main.cpp Calculator.cpp user32.lib gdi32.lib /Fe:calculator.exe
```

### 编译参数说明
- `-luser32`: 链接用户界面API库
- `-lgdi32`: 链接图形设备接口库
- `-mwindows`: 生成Windows GUI应用程序（而非控制台程序）

## 运行说明

### 直接运行
双击 `calculator.exe` 文件即可启动计算器。

### 命令行运行
```bash
.\calculator.exe
```

## 使用说明

### 基本操作
1. **数字输入**: 点击数字按钮或使用键盘数字键
2. **运算符**: 点击 +、-、×、÷ 按钮或使用键盘对应键
3. **等号**: 点击 = 按钮或按回车键/空格键
4. **小数点**: 点击 . 按钮或按小数点键
5. **清除**: 
   - C 按钮：清除所有内容，重置计算器
   - CE 按钮：仅清除当前输入的数字
   - 退格按钮：删除最后输入的字符

### 计算示例
- 简单计算：`2 + 3 = 5`
- 连续计算：`2 + 3 + 4 = 9`
- 小数计算：`3.14 × 2 = 6.28`
- 除法计算：`10 ÷ 3 = 3.3333333333`

### 错误处理
- 除零操作会显示"除零错误"
- 输入长度限制为15个字符
- 每个数字只能包含一个小数点

## 技术实现

### 架构设计
- **面向对象设计**: 使用Calculator类封装所有功能
- **分离关注点**: 界面逻辑与计算逻辑分离
- **事件驱动**: 基于Windows消息机制处理用户交互

### 核心组件
1. **Calculator类**: 主要的计算器逻辑
2. **窗口管理**: 创建和管理主窗口及控件
3. **消息处理**: 处理按钮点击和键盘输入
4. **计算引擎**: 执行数学运算和状态管理

### 依赖项
- **Windows API**: 原生Windows界面开发
- **C++ STL**: 字符串处理和数学运算
- **无第三方依赖**: 可独立运行，无需额外安装

## 开发说明

### 扩展功能
如需添加更多功能，可以考虑：
- 科学计算功能（三角函数、对数等）
- 历史记录功能
- 主题切换
- 更多键盘快捷键

### 代码结构
- `Calculator.h`: 类声明和接口定义
- `Calculator.cpp`: 核心实现逻辑
- `resource.h`: 常量和资源ID定义
- `main.cpp`: 程序入口和消息循环

## 许可证

本项目为示例代码，可自由使用和修改。
