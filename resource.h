#ifndef RESOURCE_H
#define RESOURCE_H

// 控件ID定义
#define ID_DISPLAY      1000

// 数字按钮
#define ID_0            1001
#define ID_1            1002
#define ID_2            1003
#define ID_3            1004
#define ID_4            1005
#define ID_5            1006
#define ID_6            1007
#define ID_7            1008
#define ID_8            1009
#define ID_9            1010

// 运算符按钮
#define ID_ADD          1011
#define ID_SUB          1012
#define ID_MUL          1013
#define ID_DIV          1014
#define ID_EQUAL        1015

// 功能按钮
#define ID_CLEAR        1016    // C
#define ID_CLEAR_ENTRY  1017    // CE
#define ID_DECIMAL      1018    // .
#define ID_BACKSPACE    1019    // 退格

// 窗口尺寸常量
#define WINDOW_WIDTH    280
#define WINDOW_HEIGHT   400
#define BUTTON_WIDTH    60
#define BUTTON_HEIGHT   50
#define DISPLAY_HEIGHT  40
#define MARGIN          10

#endif // RESOURCE_H
