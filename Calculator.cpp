#include "Calculator.h"
#include <sstream>
#include <iomanip>
#include <cmath>

Calculator::Calculator() : hWnd(nullptr), hDisplay(nullptr), hFont(nullptr),
    currentValue(0.0), previousValue(0.0), currentOperator(0),
    isNew<PERSON><PERSON>ber(true), hasDecimal(false), hasError(false), displayText("0") {
    
    // 初始化按钮句柄数组
    for (int i = 0; i < 20; i++) {
        hButtons[i] = nullptr;
    }
}

Calculator::~Calculator() {
    if (hFont) {
        DeleteObject(hFont);
    }
}

bool Calculator::Initialize(HINSTANCE hInstance) {
    // 注册窗口类
    WNDCLASSEX wc = {};
    wc.cbSize = sizeof(WNDCLASSEX);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.lpszClassName = "CalculatorWindow";
    wc.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);
    
    if (!RegisterClassEx(&wc)) {
        return false;
    }
    
    // 创建主窗口
    hWnd = CreateWindowEx(
        0,
        "CalculatorWindow",
        "计算器",
        WS_OVERLAPPED | WS_CAPTION | WS_SYSMENU | WS_MINIMIZEBOX,
        CW_USEDEFAULT, CW_USEDEFAULT,
        WINDOW_WIDTH, WINDOW_HEIGHT,
        nullptr, nullptr, hInstance, this
    );
    
    if (!hWnd) {
        return false;
    }
    
    // 创建字体
    hFont = CreateFont(
        20, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
        DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
        DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, "Arial"
    );
    
    // 创建控件
    CreateControls();
    
    return true;
}

void Calculator::CreateControls() {
    // 创建显示框
    hDisplay = CreateWindow(
        "EDIT", displayText.c_str(),
        WS_CHILD | WS_VISIBLE | WS_BORDER | ES_RIGHT | ES_READONLY,
        MARGIN, MARGIN, WINDOW_WIDTH - 2 * MARGIN - 20, DISPLAY_HEIGHT,
        hWnd, (HMENU)(LONG_PTR)ID_DISPLAY, nullptr, nullptr
    );
    
    if (hDisplay && hFont) {
        SendMessage(hDisplay, WM_SETFONT, (WPARAM)hFont, TRUE);
    }
    
    // 第一行：CE, C, 退格, ÷
    CreateButton(ID_CLEAR_ENTRY, "CE", MARGIN, 70);
    CreateButton(ID_CLEAR, "C", MARGIN + 70, 70);
    CreateButton(ID_BACKSPACE, "←", MARGIN + 140, 70);
    CreateButton(ID_DIV, "÷", MARGIN + 210, 70);
    
    // 第二行：7, 8, 9, ×
    CreateButton(ID_7, "7", MARGIN, 130);
    CreateButton(ID_8, "8", MARGIN + 70, 130);
    CreateButton(ID_9, "9", MARGIN + 140, 130);
    CreateButton(ID_MUL, "×", MARGIN + 210, 130);
    
    // 第三行：4, 5, 6, -
    CreateButton(ID_4, "4", MARGIN, 190);
    CreateButton(ID_5, "5", MARGIN + 70, 190);
    CreateButton(ID_6, "6", MARGIN + 140, 190);
    CreateButton(ID_SUB, "-", MARGIN + 210, 190);
    
    // 第四行：1, 2, 3, +
    CreateButton(ID_1, "1", MARGIN, 250);
    CreateButton(ID_2, "2", MARGIN + 70, 250);
    CreateButton(ID_3, "3", MARGIN + 140, 250);
    CreateButton(ID_ADD, "+", MARGIN + 210, 250);
    
    // 第五行：0, ., =
    CreateButton(ID_0, "0", MARGIN, 310, 130, BUTTON_HEIGHT);  // 0按钮更宽
    CreateButton(ID_DECIMAL, ".", MARGIN + 140, 310);
    CreateButton(ID_EQUAL, "=", MARGIN + 210, 310);
}

void Calculator::CreateButton(int id, const char* text, int x, int y, int width, int height) {
    HWND hButton = CreateWindow(
        "BUTTON", text,
        WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
        x, y, width, height,
        hWnd, (HMENU)(LONG_PTR)id, nullptr, nullptr
    );

    if (hButton && hFont) {
        SendMessage(hButton, WM_SETFONT, (WPARAM)hFont, TRUE);
    }
}

void Calculator::Show(int nCmdShow) {
    ShowWindow(hWnd, nCmdShow);
    UpdateWindow(hWnd);
}

LRESULT CALLBACK Calculator::WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    Calculator* pCalculator = nullptr;

    if (uMsg == WM_NCCREATE) {
        CREATESTRUCT* pCreate = (CREATESTRUCT*)lParam;
        pCalculator = (Calculator*)pCreate->lpCreateParams;
        SetWindowLongPtr(hWnd, GWLP_USERDATA, (LONG_PTR)pCalculator);
    } else {
        pCalculator = (Calculator*)GetWindowLongPtr(hWnd, GWLP_USERDATA);
    }

    if (pCalculator) {
        return pCalculator->HandleMessage(uMsg, wParam, lParam);
    }

    return DefWindowProc(hWnd, uMsg, wParam, lParam);
}

LRESULT Calculator::HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
        case WM_COMMAND:
            {
                int id = LOWORD(wParam);

                // 处理数字按钮
                if (id >= ID_0 && id <= ID_9) {
                    HandleNumber(id - ID_0);
                }
                // 处理运算符
                else if (id == ID_ADD || id == ID_SUB || id == ID_MUL || id == ID_DIV) {
                    char op = (id == ID_ADD) ? '+' : (id == ID_SUB) ? '-' :
                             (id == ID_MUL) ? '*' : '/';
                    HandleOperator(op);
                }
                // 处理其他功能
                else {
                    switch (id) {
                        case ID_EQUAL:
                            HandleEqual();
                            break;
                        case ID_CLEAR:
                            HandleClear();
                            break;
                        case ID_CLEAR_ENTRY:
                            HandleClearEntry();
                            break;
                        case ID_DECIMAL:
                            HandleDecimal();
                            break;
                        case ID_BACKSPACE:
                            HandleBackspace();
                            break;
                    }
                }
            }
            break;

        case WM_KEYDOWN:
            {
                // 处理键盘输入
                if (wParam >= '0' && wParam <= '9') {
                    HandleNumber(wParam - '0');
                } else {
                    switch (wParam) {
                        case VK_ADD:
                        case 187: // + key
                            HandleOperator('+');
                            break;
                        case VK_SUBTRACT:
                        case 189: // - key
                            HandleOperator('-');
                            break;
                        case VK_MULTIPLY:
                        case 56: // * key (Shift+8)
                            HandleOperator('*');
                            break;
                        case VK_DIVIDE:
                        case 191: // / key
                            HandleOperator('/');
                            break;
                        case VK_RETURN:
                        case VK_SPACE:
                            HandleEqual();
                            break;
                        case VK_DECIMAL:
                        case 190: // . key
                            HandleDecimal();
                            break;
                        case VK_BACK:
                            HandleBackspace();
                            break;
                        case VK_ESCAPE:
                            HandleClear();
                            break;
                        case VK_DELETE:
                            HandleClearEntry();
                            break;
                    }
                }
            }
            break;

        case WM_DESTROY:
            PostQuitMessage(0);
            break;

        default:
            return DefWindowProc(hWnd, uMsg, wParam, lParam);
    }

    return 0;
}

void Calculator::UpdateDisplay() {
    if (hasError) {
        SetWindowText(hDisplay, "错误");
        return;
    }

    SetWindowText(hDisplay, displayText.c_str());
}

void Calculator::HandleNumber(int digit) {
    if (hasError) {
        ResetCalculator();
    }

    if (isNewNumber) {
        displayText = std::to_string(digit);
        isNewNumber = false;
        hasDecimal = false;
    } else {
        if (displayText.length() < 15) { // 限制显示长度
            displayText += std::to_string(digit);
        }
    }

    currentValue = std::stod(displayText);
    UpdateDisplay();
}

void Calculator::HandleOperator(char op) {
    if (hasError) {
        return;
    }

    if (!isNewNumber && currentOperator != 0) {
        // 执行之前的运算
        HandleEqual();
    }

    previousValue = currentValue;
    currentOperator = op;
    isNewNumber = true;
}

void Calculator::HandleEqual() {
    if (hasError || currentOperator == 0) {
        return;
    }

    double result = PerformCalculation(previousValue, currentValue, currentOperator);

    if (hasError) {
        return;
    }

    currentValue = result;

    // 格式化显示结果
    std::ostringstream oss;
    if (result == (long long)result) {
        // 整数
        oss << (long long)result;
        displayText = oss.str();
    } else {
        // 小数，保留适当的精度
        oss << std::fixed << std::setprecision(10) << result;
        std::string str = oss.str();
        // 移除尾随的零
        str.erase(str.find_last_not_of('0') + 1, std::string::npos);
        str.erase(str.find_last_not_of('.') + 1, std::string::npos);
        displayText = str;
    }

    currentOperator = 0;
    isNewNumber = true;
    hasDecimal = displayText.find('.') != std::string::npos;
    UpdateDisplay();
}

void Calculator::HandleClear() {
    ResetCalculator();
    UpdateDisplay();
}

void Calculator::HandleClearEntry() {
    if (hasError) {
        ResetCalculator();
    } else {
        displayText = "0";
        currentValue = 0.0;
        isNewNumber = true;
        hasDecimal = false;
    }
    UpdateDisplay();
}

void Calculator::HandleDecimal() {
    if (hasError) {
        ResetCalculator();
    }

    if (isNewNumber) {
        displayText = "0.";
        isNewNumber = false;
        hasDecimal = true;
    } else if (!hasDecimal && displayText.length() < 14) {
        displayText += ".";
        hasDecimal = true;
    }

    UpdateDisplay();
}

void Calculator::HandleBackspace() {
    if (hasError || isNewNumber || displayText.length() <= 1) {
        displayText = "0";
        currentValue = 0.0;
        isNewNumber = true;
        hasDecimal = false;
    } else {
        if (displayText.back() == '.') {
            hasDecimal = false;
        }
        displayText.pop_back();
        currentValue = std::stod(displayText);
    }

    UpdateDisplay();
}

double Calculator::PerformCalculation(double a, double b, char op) {
    switch (op) {
        case '+':
            return a + b;
        case '-':
            return a - b;
        case '*':
            return a * b;
        case '/':
            if (b == 0.0) {
                ShowError("除零错误");
                return 0.0;
            }
            return a / b;
        default:
            return b;
    }
}

void Calculator::ShowError(const char* message) {
    hasError = true;
    displayText = message;
    UpdateDisplay();
}

void Calculator::ResetCalculator() {
    currentValue = 0.0;
    previousValue = 0.0;
    currentOperator = 0;
    isNewNumber = true;
    hasDecimal = false;
    hasError = false;
    displayText = "0";
}
